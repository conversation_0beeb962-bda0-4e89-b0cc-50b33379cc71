import { requireAdmin } from "@/lib/auth-utils"

export default async function AdminPage() {
    // This will automatically redirect to /login if not authenticated
    // or to /dashboard if user doesn't have admin privileges
    const session = await requireAdmin()

    return (
        <div className="p-8">
            <h1 className="text-2xl font-bold mb-4">Admin Dashboard</h1>
            <p>Welcome, {session.user.name}!</p>
            <p>Your role: {(session.user as any).role}</p>
        </div>
    )
}
