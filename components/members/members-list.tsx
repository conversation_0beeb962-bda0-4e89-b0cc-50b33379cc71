'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/components/providers/auth-provider'

type Member = {
  id: number
  firstName: string
  lastName: string
  email?: string
  phone?: string
  memberStatus: string
  cluster?: {
    id: number
    name: string
    zoneId: number
  }
}

export function MembersList() {
  const [members, setMembers] = useState<Member[]>([])
  const [loading, setLoading] = useState(true)
  const { session } = useAuth()

  useEffect(() => {
    const fetchMembers = async () => {
      try {
        const response = await fetch('/api/members', {
          headers: {
            'Authorization': `Bearer ${session?.token}`,
          },
        })
        
        if (response.ok) {
          const data = await response.json()
          setMembers(data)
        }
      } catch (error) {
        console.error('Error fetching members:', error)
      } finally {
        setLoading(false)
      }
    }

    if (session) {
      fetchMembers()
    }
  }, [session])

  if (loading) {
    return <div>Loading members...</div>
  }

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full bg-white border border-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Name
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Email
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Phone
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Cluster
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {members.map((member) => (
            <tr key={member.id}>
              <td className="px-6 py-4 whitespace-nowrap">
                {member.firstName} {member.lastName}
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                {member.email || 'N/A'}
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                {member.phone || 'N/A'}
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                {member.cluster?.name || 'N/A'}
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  member.memberStatus === 'active' 
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {member.memberStatus}
                </span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}