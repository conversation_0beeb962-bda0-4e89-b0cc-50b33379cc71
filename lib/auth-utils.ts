import { auth } from "./auth"
import { headers } from "next/headers"
import { redirect } from "next/navigation"

export type UserRole = 'super_admin' | 'senior_pastor' | 'assistant_pastor' | 'zone_minister' | 'cluster_leader' | 'dept_hod' | 'dept_assistant'

/**
 * Get the current session or redirect to login if not authenticated
 */
export async function requireAuth() {
    const session = await auth.api.getSession({
        headers: await headers()
    })

    if (!session) {
        redirect('/login')
    }

    return session
}

/**
 * Require specific roles or redirect to dashboard
 */
export async function requireRoles(allowedRoles: UserRole[]) {
    const session = await requireAuth()
    
    const userRole = (session.user as any).role as UserRole
    
    if (!allowedRoles.includes(userRole)) {
        redirect('/dashboard')
    }

    return session
}

/**
 * Check if user has admin privileges (super_admin or senior_pastor)
 */
export async function requireAdmin() {
    return requireRoles(['super_admin', 'senior_pastor'])
}

/**
 * Check if user has leadership privileges (admin + zone_minister + cluster_leader + dept_hod)
 */
export async function requireLeadership() {
    return requireRoles(['super_admin', 'senior_pastor', 'zone_minister', 'cluster_leader', 'dept_hod'])
}
