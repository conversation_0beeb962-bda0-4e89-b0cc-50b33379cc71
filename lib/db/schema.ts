import { mysqlTable, text, int, timestamp, boolean, mysqlEnum } from 'drizzle-orm/mysql-core'
import { relations } from 'drizzle-orm'

// Enums
export const roleEnum = mysqlEnum('role', [
    'super_admin',
    'senior_pastor',
    'assistant_pastor',
    'zone_minister',
    'cluster_leader',
    'dept_hod',
    'dept_assistant'
])

export const permissionLevelEnum = mysqlEnum('permission_level', ['read', 'write', 'admin'])
export const memberStatusEnum = mysqlEnum('member_status', ['active', 'inactive', 'transferred'])

// Users table (Better Auth compatible)
export const users = mysqlTable('users', {
    id: text('id').primaryKey(),
    name: text('name').notNull(),
    email: text('email').unique().notNull(),
    emailVerified: boolean('email_verified').default(false),
    image: text('image'),
    createdAt: timestamp('created_at').defaultNow(),
    updatedAt: timestamp('updated_at').defaultNow(),
    role: roleEnum.default('zone_minister'),
    isActive: boolean('is_active').default(true),
})

// Sessions table (Better Auth)
export const sessions = mysqlTable('sessions', {
    id: text('id').primaryKey(),
    expiresAt: timestamp('expires_at').notNull(),
    token: text('token').unique().notNull(),
    createdAt: timestamp('created_at').defaultNow(),
    updatedAt: timestamp('updated_at').defaultNow(),
    ipAddress: text('ip_address'),
    userAgent: text('user_agent'),
    userId: text('user_id').notNull().references(() => users.id),
})

// Accounts table (Better Auth)
export const accounts = mysqlTable('accounts', {
    id: text('id').primaryKey(),
    accountId: text('account_id').notNull(),
    providerId: text('provider_id').notNull(),
    userId: text('user_id').notNull().references(() => users.id),
    accessToken: text('access_token'),
    refreshToken: text('refresh_token'),
    idToken: text('id_token'),
    accessTokenExpiresAt: timestamp('access_token_expires_at'),
    refreshTokenExpiresAt: timestamp('refresh_token_expires_at'),
    scope: text('scope'),
    password: text('password'),
    createdAt: timestamp('created_at').defaultNow(),
    updatedAt: timestamp('updated_at').defaultNow(),
})

// Verification table (Better Auth)
export const verifications = mysqlTable('verifications', {
    id: text('id').primaryKey(),
    identifier: text('identifier').notNull(),
    value: text('value').notNull(),
    expiresAt: timestamp('expires_at').notNull(),
    createdAt: timestamp('created_at').defaultNow(),
    updatedAt: timestamp('updated_at').defaultNow(),
})

// Zones
export const zones = mysqlTable('zones', {
    id: int('id').primaryKey().autoincrement(),
    zoneName: text('zone_name').notNull(),
    zoneCode: text('zone_code').unique().notNull(),
    description: text('description'),
    isActive: boolean('is_active').default(true),
    createdAt: timestamp('created_at').defaultNow(),
})

// Clusters
export const clusters = mysqlTable('clusters', {
    id: int('id').primaryKey().autoincrement(),
    clusterName: text('cluster_name').notNull(),
    zoneId: int('zone_id').notNull().references(() => zones.id),
    description: text('description'),
    isActive: boolean('is_active').default(true),
    createdAt: timestamp('created_at').defaultNow(),
})

// Departments
export const departments = mysqlTable('departments', {
    id: int('id').primaryKey().autoincrement(),
    deptName: text('dept_name').notNull(),
    deptCode: text('dept_code').unique().notNull(),
    description: text('description'),
    isActive: boolean('is_active').default(true),
    createdAt: timestamp('created_at').defaultNow(),
})

// User Permissions
export const userPermissions = mysqlTable('user_permissions', {
    id: int('id').primaryKey().autoincrement(),
    userId: text('user_id').notNull().references(() => users.id),
    zoneId: int('zone_id').references(() => zones.id),
    clusterId: int('cluster_id').references(() => clusters.id),
    departmentId: int('department_id').references(() => departments.id),
    permissionLevel: permissionLevelEnum.default('read'),
    grantedAt: timestamp('granted_at').defaultNow(),
})

// Members
export const members = mysqlTable('members', {
    id: int('id').primaryKey().autoincrement(),
    firstName: text('first_name').notNull(),
    lastName: text('last_name').notNull(),
    email: text('email'),
    phone: text('phone'),
    address: text('address'),
    clusterId: int('cluster_id').references(() => clusters.id),
    departmentId: int('department_id').references(() => departments.id),
    memberStatus: memberStatusEnum.default('active'),
    joinDate: timestamp('join_date'),
    createdAt: timestamp('created_at').defaultNow(),
    updatedAt: timestamp('updated_at').defaultNow(),
})

// Relations
export const usersRelations = relations(users, ({ many }) => ({
    sessions: many(sessions),
    accounts: many(accounts),
    permissions: many(userPermissions),
}))

export const zonesRelations = relations(zones, ({ many }) => ({
    clusters: many(clusters),
    permissions: many(userPermissions),
}))

export const clustersRelations = relations(clusters, ({ one, many }) => ({
    zone: one(zones, {
        fields: [clusters.zoneId],
        references: [zones.id],
    }),
    members: many(members),
    permissions: many(userPermissions),
}))

export const departmentsRelations = relations(departments, ({ many }) => ({
    members: many(members),
    permissions: many(userPermissions),
}))

export const membersRelations = relations(members, ({ one }) => ({
    cluster: one(clusters, {
        fields: [members.clusterId],
        references: [clusters.id],
    }),
    department: one(departments, {
        fields: [members.departmentId],
        references: [departments.id],
    }),
}))