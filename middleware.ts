import { NextRequest, NextResponse } from "next/server"
import { getSessionCookie } from "better-auth/cookies"

export async function middleware(request: NextRequest) {
    const pathname = request.nextUrl.pathname

    // Public routes that don't require authentication
    const publicRoutes = ['/login', '/register', '/']

    if (publicRoutes.includes(pathname)) {
        return NextResponse.next()
    }

    // Check if user has a session cookie (edge-compatible)
    const sessionCookie = getSessionCookie(request)

    if (!sessionCookie) {
        return NextResponse.redirect(new URL('/login', request.url))
    }

    // Note: For role-based protection, you'll need to check roles in individual pages/routes
    // since we can't validate the full session in edge runtime

    return NextResponse.next()
}

export const config = {
    matcher: [
        '/((?!api/auth|_next/static|_next/image|favicon.ico).*)',
    ],
}